#!/bin/bash

# =============================================================================
# 交互式应用清理脚本演示
# 展示新的用户友好界面
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 显示交互式菜单
show_menu() {
    clear
    echo -e "${CYAN}=============================================="
    echo "    🧹 智能应用清理助手"
    echo "=============================================="
    echo -e "${NC}"
    echo "欢迎使用智能应用清理助手！"
    echo ""
    echo "请选择您要执行的操作："
    echo ""
    echo -e "${GREEN}1.${NC} 🔍 ${BLUE}安全搜索${NC} - 仅查找文件，不删除任何内容"
    echo -e "${GREEN}2.${NC} 🎭 ${YELLOW}预演模式${NC} - 显示将要删除的文件，但不实际删除"
    echo -e "${GREEN}3.${NC} 🗑️  ${GREEN}智能清理${NC} - 交互式删除，每步都会确认"
    echo -e "${GREEN}4.${NC} ⚡ ${RED}快速清理${NC} - 自动删除，无需确认（谨慎使用）"
    echo -e "${GREEN}5.${NC} ❓ ${CYAN}使用帮助${NC} - 查看详细说明"
    echo -e "${GREEN}6.${NC} 🚪 ${NC}退出程序"
    echo ""
    echo -e "${YELLOW}💡 提示：首次使用建议选择 '安全搜索' 或 '预演模式'${NC}"
    echo ""
}

# 获取应用程序名称
get_app_name() {
    echo -e "${CYAN}📱 请输入要清理的应用程序名称：${NC}"
    echo -e "${YELLOW}例如：ClashX Pro, Chrome, Photoshop 等${NC}"
    echo ""
    while true; do
        read -p "应用程序名称 > " app_name
        
        if [[ -z "$app_name" ]]; then
            echo -e "${RED}❌ 应用程序名称不能为空，请重新输入${NC}"
            continue
        fi
        
        echo ""
        echo -e "${GREEN}✅ 目标应用程序：${NC} \"$app_name\""
        break
    done
}

# 获取排除模式
get_exclude_options() {
    echo ""
    echo -e "${YELLOW}🛡️  是否需要保护特定文件不被删除？${NC}"
    echo -e "${CYAN}例如：如果搜索 'Clash'，但想保留 'Clash Verge'${NC}"
    echo ""
    read -p "需要设置保护模式吗？(y/N) > " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo ""
        echo -e "${YELLOW}请输入要保护的关键词（多个用空格分隔）：${NC}"
        echo -e "${CYAN}例如：\"Clash Verge\" \"Important App\"${NC}"
        read -p "保护关键词 > " exclude_input
        
        if [[ -n "$exclude_input" ]]; then
            echo ""
            echo -e "${GREEN}✅ 保护模式已设置，以下内容将被保护：${NC}"
            echo "$exclude_input" | tr ' ' '\n' | sed 's/^/  🛡️  /'
        fi
    fi
}

# 显示操作确认
show_confirmation() {
    local mode="$1"
    local app_name="$2"
    
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    📋 操作确认"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${BLUE}目标应用程序：${NC} $app_name"
    echo -e "${BLUE}操作模式：${NC} $mode"
    echo ""
    
    case "$mode" in
        "安全搜索")
            echo -e "${GREEN}✅ 此操作完全安全，只会搜索文件，不会删除任何内容${NC}"
            ;;
        "预演模式")
            echo -e "${YELLOW}🎭 此操作会显示将要删除的文件，但不会实际删除${NC}"
            ;;
        "智能清理")
            echo -e "${YELLOW}⚠️  此操作会删除文件，但每一步都会询问您的确认${NC}"
            ;;
        "快速清理")
            echo -e "${RED}⚠️  警告：此操作会自动删除文件，无法撤销！${NC}"
            ;;
    esac
    
    echo ""
    read -p "确认执行此操作吗？(y/N) > " -n 1 -r
    echo ""
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}操作已取消${NC}"
        return 1
    fi
    
    return 0
}

# 模拟执行操作
execute_operation() {
    local mode="$1"
    local app_name="$2"
    
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🚀 正在执行操作"
    echo "=============================================="
    echo -e "${NC}"
    
    # 模拟搜索过程
    echo -e "${BLUE}🔍 正在搜索 \"$app_name\" 相关文件...${NC}"
    sleep 1
    
    # 模拟找到的文件
    echo ""
    echo -e "${YELLOW}📁 搜索结果：${NC}"
    echo ""
    echo -e "${BLUE}📱 应用程序：${NC}"
    echo "  • /Applications/$app_name.app"
    echo ""
    echo -e "${BLUE}📁 用户数据：${NC}"
    echo "  • ~/Library/Application Support/$app_name"
    echo "  • ~/Library/Preferences/com.example.$app_name.plist"
    echo "  • ~/Library/Caches/com.example.$app_name"
    echo ""
    echo -e "${BLUE}⚙️  系统文件：${NC}"
    echo "  • /Library/LaunchDaemons/com.example.$app_name.plist"
    echo ""
    echo -e "${GREEN}总计找到 5 个文件/目录${NC}"
    
    case "$mode" in
        "安全搜索")
            echo ""
            echo -e "${GREEN}✅ 搜索完成！以上是找到的所有相关文件${NC}"
            echo -e "${CYAN}💡 如需删除，请重新运行脚本并选择其他模式${NC}"
            ;;
        "预演模式")
            echo ""
            echo -e "${YELLOW}🎭 预演模式：以下是将要执行的删除操作${NC}"
            echo ""
            echo -e "${CYAN}[预演] rm -rf \"/Applications/$app_name.app\"${NC}"
            echo -e "${CYAN}[预演] rm -rf \"~/Library/Application Support/$app_name\"${NC}"
            echo -e "${CYAN}[预演] rm -rf \"~/Library/Preferences/com.example.$app_name.plist\"${NC}"
            echo -e "${CYAN}[预演] rm -rf \"~/Library/Caches/com.example.$app_name\"${NC}"
            echo -e "${CYAN}[预演] sudo rm -rf \"/Library/LaunchDaemons/com.example.$app_name.plist\"${NC}"
            echo ""
            echo -e "${GREEN}✅ 预演完成！将删除 5 个文件/目录${NC}"
            echo -e "${YELLOW}💡 这是预演模式，没有实际删除任何文件${NC}"
            ;;
        "智能清理"|"快速清理")
            echo ""
            echo -e "${GREEN}🗑️  开始删除文件...${NC}"
            sleep 1
            echo -e "${GREEN}✅ 已删除：/Applications/$app_name.app${NC}"
            sleep 0.5
            echo -e "${GREEN}✅ 已删除：用户数据文件${NC}"
            sleep 0.5
            echo -e "${GREEN}✅ 已删除：系统文件${NC}"
            echo ""
            echo -e "${GREEN}🎉 清理完成！成功删除 5 个文件/目录${NC}"
            echo -e "${CYAN}💡 注意：这只是演示，实际脚本会执行真实的删除操作${NC}"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    clear
    echo -e "${CYAN}=============================================="
    echo "    📖 使用帮助"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${YELLOW}🔍 安全搜索模式：${NC}"
    echo "  • 完全安全，只搜索不删除"
    echo "  • 适合首次使用，了解应用程序在系统中的文件分布"
    echo "  • 推荐在删除前先使用此模式"
    echo ""
    echo -e "${YELLOW}🎭 预演模式：${NC}"
    echo "  • 显示将要执行的删除命令，但不实际删除"
    echo "  • 可以预览完整的删除过程"
    echo "  • 适合确认删除操作是否正确"
    echo ""
    echo -e "${YELLOW}🗑️  智能清理模式：${NC}"
    echo "  • 交互式删除，每个步骤都会询问确认"
    echo "  • 最安全的删除方式"
    echo "  • 推荐大多数用户使用"
    echo ""
    echo -e "${YELLOW}⚡ 快速清理模式：${NC}"
    echo "  • 自动删除，不询问确认"
    echo "  • 适合熟悉操作的高级用户"
    echo "  • 使用前请务必确认要删除的应用程序"
    echo ""
    echo -e "${YELLOW}🛡️  保护模式：${NC}"
    echo "  • 可以设置关键词来保护特定文件不被删除"
    echo "  • 例如：搜索'Clash'但保护'Clash Verge'"
    echo ""
    echo -e "${RED}⚠️  重要提醒：${NC}"
    echo "  • 删除操作无法撤销，请谨慎操作"
    echo "  • 建议先使用安全搜索或预演模式"
    echo "  • 删除系统重要应用可能影响系统稳定性"
    echo ""
    read -p "按回车键返回主菜单..."
}

# 主循环
main() {
    while true; do
        show_menu
        
        read -p "请选择操作 (1-6) > " choice
        
        case $choice in
            1)
                get_app_name
                get_exclude_options
                if show_confirmation "安全搜索" "$app_name"; then
                    execute_operation "安全搜索" "$app_name"
                    echo ""
                    read -p "按回车键返回主菜单..."
                fi
                ;;
            2)
                get_app_name
                get_exclude_options
                if show_confirmation "预演模式" "$app_name"; then
                    execute_operation "预演模式" "$app_name"
                    echo ""
                    read -p "按回车键返回主菜单..."
                fi
                ;;
            3)
                get_app_name
                get_exclude_options
                if show_confirmation "智能清理" "$app_name"; then
                    execute_operation "智能清理" "$app_name"
                    echo ""
                    read -p "按回车键返回主菜单..."
                fi
                ;;
            4)
                get_app_name
                get_exclude_options
                if show_confirmation "快速清理" "$app_name"; then
                    execute_operation "快速清理" "$app_name"
                    echo ""
                    read -p "按回车键返回主菜单..."
                fi
                ;;
            5)
                show_help
                ;;
            6)
                echo ""
                echo -e "${GREEN}👋 感谢使用智能应用清理助手！${NC}"
                exit 0
                ;;
            *)
                echo ""
                echo -e "${RED}❌ 无效选择，请输入 1-6${NC}"
                sleep 2
                ;;
        esac
    done
}

# 启动程序
main
