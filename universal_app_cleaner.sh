#!/bin/bash

# =============================================================================
# 通用 macOS 应用程序搜索清理脚本
# 作者: Augment Agent
# 版本: 1.0
# 描述: 搜索并清理指定应用程序的所有相关文件
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 全局变量
declare -a found_files
declare -a search_patterns
declare -a exclude_patterns
APP_NAME=""
SEARCH_MODE="interactive"  # interactive, search-only, auto-clean, dry-run
DRY_RUN=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo -e "${CYAN}通用 macOS 应用程序清理脚本${NC}"
    echo ""
    echo "用法:"
    echo "  $0 [选项] <应用程序名称>"
    echo ""
    echo "选项:"
    echo "  -s, --search-only     仅搜索，不删除文件"
    echo "  -d, --dry-run         预演模式，显示将要执行的操作但不实际删除"
    echo "  -a, --auto-clean      自动清理，不询问确认"
    echo "  -e, --exclude PATTERN 排除匹配模式的文件"
    echo "  -h, --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 \"ClashX Pro\"                    # 交互式清理ClashX Pro"
    echo "  $0 -s \"Clash\"                     # 仅搜索Clash相关文件"
    echo "  $0 -d \"ClashX Pro\"                # 预演删除操作，不实际执行"
    echo "  $0 -e \"Clash Verge\" \"Clash\"       # 清理Clash但排除Clash Verge"
    echo "  $0 -a \"Unwanted App\"              # 自动清理，无需确认"
    echo ""
}

# 检查是否为macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "此脚本仅适用于 macOS 系统"
        exit 1
    fi
}

# 生成搜索模式
generate_search_patterns() {
    local app_name="$1"
    search_patterns=()
    
    # 基于应用名称生成多种搜索模式
    search_patterns+=("$app_name")
    search_patterns+=("*$app_name*")
    
    # 生成无空格版本
    local no_space="${app_name// /}"
    if [[ "$no_space" != "$app_name" ]]; then
        search_patterns+=("$no_space")
        search_patterns+=("*$no_space*")
    fi
    
    # 生成小写版本
    local lowercase=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')
    if [[ "$lowercase" != "$app_name" ]]; then
        search_patterns+=("$lowercase")
        search_patterns+=("*$lowercase*")
    fi

    # 生成首字母大写版本
    local capitalized=$(echo "${app_name:0:1}" | tr '[:lower:]' '[:upper:]')$(echo "${app_name:1}" | tr '[:upper:]' '[:lower:]')
    if [[ "$capitalized" != "$app_name" ]]; then
        search_patterns+=("$capitalized")
        search_patterns+=("*$capitalized*")
    fi
    
    # 去重
    local temp_file=$(mktemp)
    printf '%s\n' "${search_patterns[@]}" | sort -u > "$temp_file"
    search_patterns=()
    while IFS= read -r line; do
        search_patterns+=("$line")
    done < "$temp_file"
    rm -f "$temp_file"
}

# 搜索应用相关文件
search_app_files() {
    log_info "正在搜索 \"$APP_NAME\" 相关文件..."
    
    # 清空结果数组
    found_files=()
    
    # 生成搜索模式
    generate_search_patterns "$APP_NAME"
    
    # 搜索 Applications 目录
    for pattern in "${search_patterns[@]}"; do
        while IFS= read -r -d '' file; do
            if should_include_file "$file"; then
                found_files+=("$file")
            fi
        done < <(find "/Applications" -maxdepth 2 -name "$pattern" -print0 2>/dev/null || true)
    done
    
    # 定义搜索目录
    local user_dirs=(
        "$HOME/Library/Application Support"
        "$HOME/Library/WebKit"
        "$HOME/Library/Preferences"
        "$HOME/Library/HTTPStorages"
        "$HOME/Library/Logs"
        "$HOME/Library/Caches"
        "$HOME/Library/Application Scripts"
        "$HOME/Library/LaunchAgents"
        "$HOME/Library/Containers"
        "$HOME/Library/Group Containers"
        "$HOME/Library/Saved Application State"
    )
    
    local system_dirs=(
        "/Library/LaunchDaemons"
        "/Library/LaunchAgents"
        "/Library/Application Support"
        "/Library/PrivilegedHelperTools"
        "/Library/Preferences"
    )
    
    # 搜索用户目录
    for dir in "${user_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            for pattern in "${search_patterns[@]}"; do
                while IFS= read -r -d '' file; do
                    if should_include_file "$file"; then
                        found_files+=("$file")
                    fi
                done < <(find "$dir" -maxdepth 3 -name "$pattern" -print0 2>/dev/null || true)
            done
        fi
    done
    
    # 搜索系统目录
    for dir in "${system_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            for pattern in "${search_patterns[@]}"; do
                while IFS= read -r -d '' file; do
                    if should_include_file "$file"; then
                        found_files+=("$file")
                    fi
                done < <(find "$dir" -maxdepth 3 -name "$pattern" -print0 2>/dev/null || true)
            done
        fi
    done
    
    # 去重和排序
    if [[ ${#found_files[@]} -gt 0 ]]; then
        local temp_file=$(mktemp)
        printf '%s\n' "${found_files[@]}" | sort -u > "$temp_file"
        
        found_files=()
        while IFS= read -r line; do
            found_files+=("$line")
        done < "$temp_file"
        
        rm -f "$temp_file"
    fi
}

# 检查文件是否应该包含在结果中
should_include_file() {
    local file="$1"
    
    # 检查排除模式
    for exclude_pattern in "${exclude_patterns[@]}"; do
        if [[ "$file" =~ $exclude_pattern ]]; then
            return 1  # 排除此文件
        fi
    done
    
    return 0  # 包含此文件
}

# 显示搜索结果
show_search_results() {
    if [[ ${#found_files[@]} -eq 0 ]]; then
        log_info "未找到任何 \"$APP_NAME\" 相关文件"
        return 1
    fi
    
    echo -e "${YELLOW}🔍 搜索到以下 \"$APP_NAME\" 相关文件：${NC}"
    echo ""
    
    local app_files=()
    local user_files=()
    local system_files=()
    
    # 分类文件
    for file in "${found_files[@]}"; do
        if [[ "$file" =~ ^/Applications/ ]]; then
            app_files+=("$file")
        elif [[ "$file" =~ ^/Library/ ]]; then
            system_files+=("$file")
        else
            user_files+=("$file")
        fi
    done
    
    # 显示应用程序
    if [[ ${#app_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📱 应用程序：${NC}"
        for file in "${app_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi
    
    # 显示用户文件
    if [[ ${#user_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📁 用户数据：${NC}"
        for file in "${user_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi
    
    # 显示系统文件
    if [[ ${#system_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}⚙️  系统文件：${NC}"
        for file in "${system_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi
    
    echo -e "${GREEN}总计找到 ${#found_files[@]} 个文件/目录${NC}"
    echo ""
    
    return 0
}

# 显示搜索模式
show_search_patterns() {
    echo -e "${CYAN}🔍 使用的搜索模式：${NC}"
    for pattern in "${search_patterns[@]}"; do
        echo "  • $pattern"
    done
    echo ""
    
    if [[ ${#exclude_patterns[@]} -gt 0 ]]; then
        echo -e "${CYAN}🚫 排除模式：${NC}"
        for pattern in "${exclude_patterns[@]}"; do
            echo "  • $pattern"
        done
        echo ""
    fi
}

# 用户确认
confirm_removal() {
    if [[ "$SEARCH_MODE" == "auto-clean" ]]; then
        return 0
    fi
    
    echo -e "${RED}⚠️  警告：此操作不可逆！${NC}"
    echo ""
    read -p "确定要删除这些文件吗？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        return 1
    fi
    return 0
}

# 删除文件
remove_files() {
    if [[ ${#found_files[@]} -eq 0 ]]; then
        log_warning "没有找到需要删除的文件"
        return
    fi

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "🎭 预演模式：显示将要执行的删除操作"
        echo ""
    else
        log_info "开始删除找到的文件..."
    fi

    local deleted_count=0
    local failed_count=0
    local system_files=()
    local user_files=()

    # 分类文件
    for file in "${found_files[@]}"; do
        if [[ "$file" =~ ^/Library/ ]]; then
            system_files+=("$file")
        else
            user_files+=("$file")
        fi
    done

    # 删除用户文件
    for file in "${user_files[@]}"; do
        if [[ -e "$file" ]]; then
            if [[ "$DRY_RUN" == "true" ]]; then
                echo -e "${CYAN}[DRY-RUN]${NC} 将删除: $file"
                ((deleted_count++))
            else
                log_info "删除: $file"
                rm -rf "$file"
                if [[ $? -eq 0 ]]; then
                    log_success "已删除: $(basename "$file")"
                    ((deleted_count++))
                else
                    log_error "删除失败: $file"
                    ((failed_count++))
                fi
            fi
        else
            if [[ "$DRY_RUN" == "true" ]]; then
                echo -e "${YELLOW}[DRY-RUN]${NC} 文件不存在: $file"
            fi
        fi
    done

    # 删除系统文件
    if [[ ${#system_files[@]} -gt 0 ]]; then
        if [[ "$DRY_RUN" == "true" ]]; then
            echo -e "${CYAN}[DRY-RUN]${NC} 将删除系统文件（需要管理员权限）:"
        else
            log_info "删除系统文件（需要管理员权限）..."
        fi

        for file in "${system_files[@]}"; do
            if [[ -e "$file" ]]; then
                if [[ "$DRY_RUN" == "true" ]]; then
                    echo -e "${CYAN}[DRY-RUN]${NC} sudo rm -rf \"$file\""
                    ((deleted_count++))
                else
                    log_info "删除系统文件: $file"
                    sudo rm -rf "$file"
                    if [[ $? -eq 0 ]]; then
                        log_success "已删除: $(basename "$file")"
                        ((deleted_count++))
                    else
                        log_error "删除失败: $file"
                        ((failed_count++))
                    fi
                fi
            else
                if [[ "$DRY_RUN" == "true" ]]; then
                    echo -e "${YELLOW}[DRY-RUN]${NC} 系统文件不存在: $file"
                fi
            fi
        done
    fi

    # 报告结果
    echo ""
    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "预演操作完成："
        log_success "将删除: $deleted_count 个文件/目录"
        echo -e "${YELLOW}💡 这是预演模式，没有实际删除任何文件${NC}"
    else
        log_info "删除操作完成："
        log_success "成功删除: $deleted_count 个文件/目录"
        if [[ $failed_count -gt 0 ]]; then
            log_error "删除失败: $failed_count 个文件/目录"
        fi
    fi
}

# 解析命令行参数
parse_arguments() {
    exclude_patterns=()
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -s|--search-only)
                SEARCH_MODE="search-only"
                shift
                ;;
            -d|--dry-run)
                SEARCH_MODE="dry-run"
                DRY_RUN=true
                shift
                ;;
            -a|--auto-clean)
                SEARCH_MODE="auto-clean"
                shift
                ;;
            -e|--exclude)
                if [[ -n "$2" ]]; then
                    exclude_patterns+=("$2")
                    shift 2
                else
                    log_error "选项 $1 需要一个参数"
                    exit 1
                fi
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$APP_NAME" ]]; then
                    APP_NAME="$1"
                else
                    log_error "只能指定一个应用程序名称"
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    if [[ -z "$APP_NAME" ]]; then
        log_error "请指定要搜索的应用程序名称"
        show_usage
        exit 1
    fi
}

# 主函数
main() {
    # 检查系统
    check_macos
    
    # 解析参数
    parse_arguments "$@"
    
    # 显示横幅
    echo -e "${CYAN}=============================================="
    echo "    通用 macOS 应用程序清理脚本"
    echo "=============================================="
    echo -e "${NC}"
    echo "目标应用程序: $APP_NAME"
    echo "操作模式: $SEARCH_MODE"
    echo ""
    
    # 搜索文件
    search_app_files
    
    # 显示搜索模式
    show_search_patterns
    
    # 显示搜索结果
    if ! show_search_results; then
        exit 0
    fi
    
    # 根据模式执行操作
    case "$SEARCH_MODE" in
        "search-only")
            log_info "仅搜索模式，不执行删除操作"
            ;;
        "dry-run")
            log_info "预演模式，将显示删除操作但不实际执行"
            remove_files
            ;;
        "interactive"|"auto-clean")
            if confirm_removal; then
                remove_files

                # 验证清理结果（仅在非预演模式下）
                if [[ "$DRY_RUN" != "true" ]]; then
                    log_info "验证清理结果..."
                    search_app_files
                    if [[ ${#found_files[@]} -eq 0 ]]; then
                        log_success "✅ \"$APP_NAME\" 已完全清理！"
                    else
                        log_warning "发现 ${#found_files[@]} 个残留文件"
                    fi
                fi
            fi
            ;;
    esac
    
    echo ""
    log_success "操作完成！"
}

# 错误处理
set -e
trap 'log_error "脚本执行过程中发生错误，请检查输出信息"' ERR

# 运行主函数
main "$@"
