#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 全局变量
APP_NAME=""
found_files=()
exclude_patterns=()
search_patterns=()
DRY_RUN=false

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示欢迎界面
show_welcome() {
    clear
    echo -e "${CYAN}=============================================="
    echo "    🧹 智能应用清理助手"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${GREEN}欢迎使用智能应用清理助手！${NC}"
    echo ""
    echo -e "${YELLOW}💡 推荐使用流程：${NC}"
    echo -e "${BLUE}第一步：${NC} 🔍 安全搜索 - 了解应用在系统中的文件分布"
    echo -e "${BLUE}第二步：${NC} 🎭 预演删除 - 确认删除操作是否正确"
    echo -e "${BLUE}第三步：${NC} 🗑️  执行清理 - 实际删除文件"
    echo ""
    echo -e "${PURPLE}✨ 智能功能：${NC}"
    echo -e "${CYAN}   • 真实的文件系统搜索${NC}"
    echo -e "${CYAN}   • 基于搜索结果的智能保护模式${NC}"
    echo -e "${CYAN}   • 安全的预演和实际删除功能${NC}"
    echo ""
    read -p "按回车键开始..."
    clear
}

# 获取应用程序名称
get_app_name() {
    echo -e "${CYAN}=============================================="
    echo "    📱 输入目标应用程序"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${YELLOW}请输入要清理的应用程序名称：${NC}"
    echo ""
    echo -e "${CYAN}💡 输入提示：${NC}"
    echo "  • 可以输入完整名称：ClashX Pro"
    echo "  • 也可以输入关键词：Clash"
    echo "  • 支持中英文应用名称"
    echo ""
    
    while true; do
        read -p "应用程序名称 > " input_name
        
        if [[ -z "$input_name" ]]; then
            echo -e "${RED}❌ 应用程序名称不能为空，请重新输入${NC}"
            continue
        fi
        
        APP_NAME="$input_name"
        echo ""
        echo -e "${GREEN}✅ 目标应用程序：${NC} \"$APP_NAME\""
        break
    done
}

# 生成搜索模式
generate_search_patterns() {
    local app_name="$1"
    search_patterns=()
    
    # 基于应用名称生成多种搜索模式
    search_patterns+=("$app_name")
    search_patterns+=("*$app_name*")
    
    # 生成无空格版本
    local no_space="${app_name// /}"
    if [[ "$no_space" != "$app_name" ]]; then
        search_patterns+=("$no_space")
        search_patterns+=("*$no_space*")
    fi
    
    # 生成小写版本
    local lowercase=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')
    if [[ "$lowercase" != "$app_name" ]]; then
        search_patterns+=("$lowercase")
        search_patterns+=("*$lowercase*")
    fi
    
    # 生成首字母大写版本
    local capitalized=$(echo "${app_name:0:1}" | tr '[:lower:]' '[:upper:]')$(echo "${app_name:1}" | tr '[:upper:]' '[:lower:]')
    if [[ "$capitalized" != "$app_name" ]]; then
        search_patterns+=("$capitalized")
        search_patterns+=("*$capitalized*")
    fi
    
    # 去重和排序
    if [[ ${#search_patterns[@]} -gt 0 ]]; then
        local temp_file=$(mktemp)
        printf '%s\n' "${search_patterns[@]}" | sort -u > "$temp_file"
        search_patterns=()
        while IFS= read -r line; do
            search_patterns+=("$line")
        done < "$temp_file"
        rm -f "$temp_file"
    fi
}

# 检查文件是否应该包含在结果中
should_include_file() {
    local file="$1"
    
    # 检查排除模式
    for exclude_pattern in "${exclude_patterns[@]}"; do
        if [[ "$file" =~ $exclude_pattern ]]; then
            return 1  # 排除此文件
        fi
    done
    
    return 0  # 包含此文件
}

# 真实搜索文件
search_files() {
    echo ""
    echo -e "${BLUE}🔍 正在搜索 \"$APP_NAME\" 相关文件...${NC}"
    
    # 清空结果数组
    found_files=()
    
    # 生成搜索模式
    generate_search_patterns "$APP_NAME"
    
    # 搜索 Applications 目录
    for pattern in "${search_patterns[@]}"; do
        while IFS= read -r -d '' file; do
            if should_include_file "$file"; then
                found_files+=("$file")
            fi
        done < <(find "/Applications" -maxdepth 2 -name "$pattern" -print0 2>/dev/null || true)
    done
    
    # 定义搜索目录
    local user_dirs=(
        "$HOME/Library/Application Support"
        "$HOME/Library/WebKit"
        "$HOME/Library/Preferences"
        "$HOME/Library/HTTPStorages"
        "$HOME/Library/Logs"
        "$HOME/Library/Caches"
        "$HOME/Library/Application Scripts"
        "$HOME/Library/LaunchAgents"
        "$HOME/Library/Containers"
        "$HOME/Library/Group Containers"
        "$HOME/Library/Saved Application State"
    )
    
    local system_dirs=(
        "/Library/LaunchDaemons"
        "/Library/LaunchAgents"
        "/Library/Application Support"
        "/Library/PrivilegedHelperTools"
        "/Library/Preferences"
    )
    
    # 搜索用户目录
    for dir in "${user_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            for pattern in "${search_patterns[@]}"; do
                while IFS= read -r -d '' file; do
                    if should_include_file "$file"; then
                        found_files+=("$file")
                    fi
                done < <(find "$dir" -maxdepth 3 -name "$pattern" -print0 2>/dev/null || true)
            done
        fi
    done
    
    # 搜索系统目录
    for dir in "${system_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            for pattern in "${search_patterns[@]}"; do
                while IFS= read -r -d '' file; do
                    if should_include_file "$file"; then
                        found_files+=("$file")
                    fi
                done < <(find "$dir" -maxdepth 3 -name "$pattern" -print0 2>/dev/null || true)
            done
        fi
    done
    
    # 去重和排序
    if [[ ${#found_files[@]} -gt 0 ]]; then
        local temp_file=$(mktemp)
        printf '%s\n' "${found_files[@]}" | sort -u > "$temp_file"
        
        found_files=()
        while IFS= read -r line; do
            found_files+=("$line")
        done < "$temp_file"
        
        rm -f "$temp_file"
    fi
}

# 显示搜索结果并分析
show_search_results() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    📋 搜索结果分析"
    echo "=============================================="
    echo -e "${NC}"
    
    if [[ ${#found_files[@]} -eq 0 ]]; then
        echo -e "${YELLOW}🤔 未找到 \"$APP_NAME\" 相关文件${NC}"
        echo ""
        echo "可能的原因："
        echo "  • 应用程序未安装"
        echo "  • 应用程序名称不匹配"
        echo "  • 应用程序已被完全清理"
        echo ""
        return 1
    fi
    
    echo -e "${GREEN}🎯 找到 ${#found_files[@]} 个相关文件/目录：${NC}"
    echo ""
    
    # 分类显示
    local app_files=()
    local data_files=()
    
    for file in "${found_files[@]}"; do
        if [[ "$file" =~ \.app$ ]]; then
            app_files+=("$file")
        else
            data_files+=("$file")
        fi
    done
    
    if [[ ${#app_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📱 应用程序：${NC}"
        for file in "${app_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi
    
    if [[ ${#data_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📁 数据文件：${NC}"
        for file in "${data_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi
    
    # 智能分析是否需要保护模式
    analyze_protection_need
    
    return 0
}

# 智能分析是否需要保护模式
analyze_protection_need() {
    local need_protection=false
    local protection_suggestions=()
    
    # 检查是否有多个相似应用
    local app_count=0
    local different_apps=()
    
    for file in "${found_files[@]}"; do
        if [[ "$file" =~ \.app$ ]]; then
            ((app_count++))
            local app_name=$(basename "$file" .app)
            different_apps+=("$app_name")
        fi
    done
    
    if [[ $app_count -gt 1 ]]; then
        need_protection=true
        echo -e "${YELLOW}⚠️  检测到多个相关应用程序：${NC}"
        for app in "${different_apps[@]}"; do
            echo "  • $app"
        done
        echo ""
        echo -e "${CYAN}💡 建议：如果您只想删除其中某个应用，请使用保护模式${NC}"
        
        # 生成保护建议
        for app in "${different_apps[@]}"; do
            if [[ "$app" != "$APP_NAME" ]]; then
                protection_suggestions+=("$app")
            fi
        done
    else
        echo -e "${GREEN}✅ 搜索结果明确，找到单一目标应用${NC}"
        echo -e "${CYAN}💡 无需设置保护模式，可以直接进行清理${NC}"
    fi
    
    echo ""
    
    # 如果需要保护模式，提供选项
    if [[ "$need_protection" == "true" ]]; then
        offer_protection_mode "${protection_suggestions[@]}"
    fi
}

# 提供保护模式选项
offer_protection_mode() {
    local suggestions=("$@")

    echo -e "${YELLOW}🛡️  是否需要设置保护模式？${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 是 - 保护某些应用不被删除"
    echo -e "${GREEN}2.${NC} 否 - 删除所有找到的文件"
    echo ""

    read -p "请选择 (1/2) > " protection_choice

    if [[ "$protection_choice" == "1" ]]; then
        echo ""
        echo -e "${CYAN}建议保护以下应用：${NC}"
        for i in "${!suggestions[@]}"; do
            echo -e "${GREEN}$((i+1)).${NC} ${suggestions[i]}"
        done
        echo -e "${GREEN}$((${#suggestions[@]}+1)).${NC} 自定义输入"
        echo ""

        read -p "请选择要保护的应用 (多选用空格分隔，如: 1 2) > " protect_choices

        exclude_patterns=()
        for choice in $protect_choices; do
            if [[ "$choice" =~ ^[0-9]+$ ]] && [[ "$choice" -le "${#suggestions[@]}" ]]; then
                exclude_patterns+=("${suggestions[$((choice-1))]}")
            elif [[ "$choice" == "$((${#suggestions[@]}+1))" ]]; then
                echo "请输入自定义保护关键词："
                read -p "> " custom_protection
                if [[ -n "$custom_protection" ]]; then
                    exclude_patterns+=("$custom_protection")
                fi
            fi
        done

        if [[ ${#exclude_patterns[@]} -gt 0 ]]; then
            echo ""
            echo -e "${GREEN}✅ 保护模式已设置，以下内容将被保护：${NC}"
            for pattern in "${exclude_patterns[@]}"; do
                echo "  🛡️  $pattern"
            done
        fi
    fi
    echo ""
}

# 显示操作菜单
show_action_menu() {
    echo -e "${CYAN}=============================================="
    echo "    🎯 选择执行操作"
    echo "=============================================="
    echo -e "${NC}"
    echo "基于搜索结果，您可以执行以下操作："
    echo ""
    echo -e "${GREEN}1.${NC} 🎭 ${YELLOW}预演删除${NC} - 显示将要删除的文件（推荐下一步）"
    echo -e "${GREEN}2.${NC} 🗑️  ${GREEN}智能清理${NC} - 交互式删除，逐步确认"
    echo -e "${GREEN}3.${NC} ⚡ ${RED}快速清理${NC} - 自动删除所有文件"
    echo -e "${GREEN}4.${NC} 🔍 ${BLUE}重新搜索${NC} - 修改搜索条件"
    echo -e "${GREEN}5.${NC} 🚪 ${NC}退出程序"
    echo ""
    echo -e "${YELLOW}💡 建议：首次使用请选择 '预演删除' 确认操作${NC}"
    echo ""
}

# 预演删除操作
preview_deletion() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🎭 预演删除操作"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${YELLOW}以下是将要执行的删除操作（仅预览，不会实际删除）：${NC}"
    echo ""

    local will_delete=()
    local will_protect=()

    # 根据保护模式过滤文件
    for file in "${found_files[@]}"; do
        local should_protect=false

        for pattern in "${exclude_patterns[@]}"; do
            if [[ "$file" =~ $pattern ]]; then
                should_protect=true
                will_protect+=("$file")
                break
            fi
        done

        if [[ "$should_protect" == "false" ]]; then
            will_delete+=("$file")
        fi
    done

    # 显示将要删除的文件
    if [[ ${#will_delete[@]} -gt 0 ]]; then
        echo -e "${RED}🗑️  将要删除的文件：${NC}"
        for file in "${will_delete[@]}"; do
            echo -e "${CYAN}[预演] rm -rf \"$file\"${NC}"
        done
        echo ""
        echo -e "${GREEN}总计将删除：${#will_delete[@]} 个文件/目录${NC}"
    else
        echo -e "${YELLOW}⚠️  根据保护设置，没有文件将被删除${NC}"
    fi

    # 显示受保护的文件
    if [[ ${#will_protect[@]} -gt 0 ]]; then
        echo ""
        echo -e "${GREEN}🛡️  受保护的文件（将保留）：${NC}"
        for file in "${will_protect[@]}"; do
            echo "  • $file"
        done
    fi

    echo ""
    echo -e "${YELLOW}💡 这是预演模式，没有实际删除任何文件${NC}"
    echo ""
}

# 实际删除文件
execute_deletion() {
    local mode="$1"  # interactive 或 auto

    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🗑️  执行删除操作"
    echo "=============================================="
    echo -e "${NC}"

    local will_delete=()
    local will_protect=()

    # 根据保护模式过滤文件
    for file in "${found_files[@]}"; do
        local should_protect=false

        for pattern in "${exclude_patterns[@]}"; do
            if [[ "$file" =~ $pattern ]]; then
                should_protect=true
                will_protect+=("$file")
                break
            fi
        done

        if [[ "$should_protect" == "false" ]]; then
            will_delete+=("$file")
        fi
    done

    if [[ ${#will_delete[@]} -eq 0 ]]; then
        log_warning "根据保护设置，没有文件需要删除"
        return
    fi

    # 最终确认
    if [[ "$mode" == "interactive" ]]; then
        echo -e "${RED}⚠️  即将删除 ${#will_delete[@]} 个文件/目录${NC}"
        echo -e "${YELLOW}此操作不可撤销！${NC}"
        echo ""
        read -p "确定要继续吗？(y/N) > " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            return
        fi
    fi

    log_info "开始删除文件..."

    local deleted_count=0
    local failed_count=0
    local system_files=()
    local user_files=()

    # 分类文件
    for file in "${will_delete[@]}"; do
        if [[ "$file" =~ ^/Library/ ]]; then
            system_files+=("$file")
        else
            user_files+=("$file")
        fi
    done

    # 删除用户文件
    for file in "${user_files[@]}"; do
        if [[ -e "$file" ]]; then
            log_info "删除: $file"
            rm -rf "$file"
            if [[ $? -eq 0 ]]; then
                log_success "已删除: $(basename "$file")"
                ((deleted_count++))
            else
                log_error "删除失败: $file"
                ((failed_count++))
            fi
        fi
    done

    # 删除系统文件
    if [[ ${#system_files[@]} -gt 0 ]]; then
        log_info "删除系统文件（需要管理员权限）..."
        for file in "${system_files[@]}"; do
            if [[ -e "$file" ]]; then
                log_info "删除系统文件: $file"
                sudo rm -rf "$file"
                if [[ $? -eq 0 ]]; then
                    log_success "已删除: $(basename "$file")"
                    ((deleted_count++))
                else
                    log_error "删除失败: $file"
                    ((failed_count++))
                fi
            fi
        done
    fi

    # 报告结果
    echo ""
    log_info "删除操作完成："
    log_success "成功删除: $deleted_count 个文件/目录"
    if [[ $failed_count -gt 0 ]]; then
        log_error "删除失败: $failed_count 个文件/目录"
    fi

    # 显示受保护的文件
    if [[ ${#will_protect[@]} -gt 0 ]]; then
        echo ""
        log_info "受保护的文件（已保留）："
        for file in "${will_protect[@]}"; do
            echo "  🛡️  $file"
        done
    fi
}

# 主流程
main() {
    show_welcome

    while true; do
        # 第一步：获取应用名称
        get_app_name

        # 第二步：搜索文件
        search_files

        # 第三步：显示搜索结果和智能分析
        if ! show_search_results; then
            echo -e "${YELLOW}是否要重新搜索？(y/N)${NC}"
            read -p "> " -n 1 -r
            echo ""
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                continue
            else
                echo -e "${GREEN}👋 感谢使用！${NC}"
                exit 0
            fi
        fi

        # 第四步：选择操作
        while true; do
            show_action_menu
            read -p "请选择操作 (1-5) > " action_choice

            case $action_choice in
                1)
                    preview_deletion
                    read -p "按回车键继续..."
                    ;;
                2)
                    execute_deletion "interactive"
                    read -p "按回车键继续..."
                    ;;
                3)
                    execute_deletion "auto"
                    read -p "按回车键继续..."
                    ;;
                4)
                    break  # 重新搜索
                    ;;
                5)
                    echo ""
                    echo -e "${GREEN}👋 感谢使用完整版智能应用清理助手！${NC}"
                    exit 0
                    ;;
                *)
                    echo ""
                    echo -e "${RED}❌ 无效选择，请输入 1-5${NC}"
                    sleep 1
                    ;;
            esac
        done
    done
}

# 启动程序
main
