#!/bin/bash

# =============================================================================
# Clash X Pro 完全卸载脚本
# 作者: Augment Agent
# 版本: 1.0
# 描述: 彻底清理 Clash X Pro，保留 Clash Verge
# =============================================================================

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "此脚本仅适用于 macOS 系统"
        exit 1
    fi
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "    Clash X Pro 完全卸载脚本"
    echo "=============================================="
    echo -e "${NC}"
    echo "此脚本将彻底删除 Clash X Pro 及其所有相关文件"
    echo "⚠️  注意：Clash Verge 将被保留"
    echo ""
}

# 显示将要删除的文件列表
show_files_to_remove() {
    echo -e "${YELLOW}将要删除的文件和目录：${NC}"
    echo ""
    echo "📱 应用程序："
    echo "  • /Applications/ClashX Pro.app"
    echo ""
    echo "📁 用户数据："
    echo "  • ~/Library/Application Support/com.west2online.ClashXPro"
    echo "  • ~/Library/WebKit/com.west2online.ClashXPro"
    echo "  • ~/Library/Preferences/com.west2online.ClashXPro.plist"
    echo "  • ~/Library/HTTPStorages/com.west2online.ClashXPro"
    echo "  • ~/Library/Logs/ClashX Pro"
    echo ""
    echo "⚙️  系统服务："
    echo "  • /Library/LaunchDaemons/com.west2online.ClashXPro.ProxyConfigHelper.plist"
    echo ""
}

# 用户确认
confirm_removal() {
    echo -e "${RED}⚠️  警告：此操作不可逆！${NC}"
    echo ""
    read -p "确定要继续删除 Clash X Pro 吗？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 停止 Clash X Pro 进程
stop_clashx_processes() {
    log_info "正在停止 Clash X Pro 进程..."
    
    # 查找并终止 Clash X Pro 进程
    if pgrep -f "ClashX Pro" > /dev/null; then
        log_info "发现运行中的 Clash X Pro 进程，正在终止..."
        pkill -f "ClashX Pro" 2>/dev/null || true
        sleep 2
        
        # 强制终止如果还在运行
        if pgrep -f "ClashX Pro" > /dev/null; then
            log_warning "强制终止 Clash X Pro 进程..."
            pkill -9 -f "ClashX Pro" 2>/dev/null || true
        fi
        log_success "Clash X Pro 进程已停止"
    else
        log_info "未发现运行中的 Clash X Pro 进程"
    fi
}

# 卸载系统服务
unload_system_services() {
    log_info "正在卸载系统服务..."
    
    local daemon_plist="/Library/LaunchDaemons/com.west2online.ClashXPro.ProxyConfigHelper.plist"
    
    if [[ -f "$daemon_plist" ]]; then
        log_info "卸载 LaunchDaemon 服务..."
        sudo launchctl unload "$daemon_plist" 2>/dev/null || true
        log_success "系统服务已卸载"
    else
        log_info "未发现系统服务文件"
    fi
}

# 删除文件和目录
remove_files() {
    log_info "开始删除 Clash X Pro 文件..."
    
    # 定义要删除的文件和目录
    local files_to_remove=(
        "/Applications/ClashX Pro.app"
        "$HOME/Library/Application Support/com.west2online.ClashXPro"
        "$HOME/Library/WebKit/com.west2online.ClashXPro"
        "$HOME/Library/Preferences/com.west2online.ClashXPro.plist"
        "$HOME/Library/HTTPStorages/com.west2online.ClashXPro"
        "$HOME/Library/Logs/ClashX Pro"
    )
    
    # 删除用户文件
    for file in "${files_to_remove[@]}"; do
        if [[ -e "$file" ]]; then
            log_info "删除: $file"
            rm -rf "$file"
            if [[ $? -eq 0 ]]; then
                log_success "已删除: $(basename "$file")"
            else
                log_error "删除失败: $file"
            fi
        else
            log_info "文件不存在: $(basename "$file")"
        fi
    done
    
    # 删除系统文件（需要sudo权限）
    local system_file="/Library/LaunchDaemons/com.west2online.ClashXPro.ProxyConfigHelper.plist"
    if [[ -f "$system_file" ]]; then
        log_info "删除系统服务文件..."
        sudo rm -f "$system_file"
        if [[ $? -eq 0 ]]; then
            log_success "已删除系统服务文件"
        else
            log_error "删除系统服务文件失败"
        fi
    else
        log_info "系统服务文件不存在"
    fi
}

# 验证清理结果
verify_removal() {
    log_info "验证清理结果..."
    
    local remaining_files=()
    
    # 检查是否还有残留文件
    if [[ -d "/Applications/ClashX Pro.app" ]]; then
        remaining_files+=("/Applications/ClashX Pro.app")
    fi
    
    # 检查用户目录
    local user_patterns=(
        "$HOME/Library/Application Support/com.west2online.ClashXPro"
        "$HOME/Library/WebKit/com.west2online.ClashXPro"
        "$HOME/Library/Preferences/com.west2online.ClashXPro.plist"
        "$HOME/Library/HTTPStorages/com.west2online.ClashXPro"
        "$HOME/Library/Logs/ClashX Pro"
    )
    
    for pattern in "${user_patterns[@]}"; do
        if [[ -e "$pattern" ]]; then
            remaining_files+=("$pattern")
        fi
    done
    
    # 检查系统文件
    if [[ -f "/Library/LaunchDaemons/com.west2online.ClashXPro.ProxyConfigHelper.plist" ]]; then
        remaining_files+=("/Library/LaunchDaemons/com.west2online.ClashXPro.ProxyConfigHelper.plist")
    fi
    
    # 报告结果
    if [[ ${#remaining_files[@]} -eq 0 ]]; then
        log_success "✅ Clash X Pro 已完全清理！"
        echo ""
        log_info "验证 Clash Verge 状态..."
        if [[ -d "/Applications/Clash Verge.app" ]]; then
            log_success "✅ Clash Verge 保持完整"
        else
            log_info "系统中未发现 Clash Verge"
        fi
    else
        log_warning "发现残留文件："
        for file in "${remaining_files[@]}"; do
            echo "  • $file"
        done
        echo ""
        log_warning "请手动删除这些文件"
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    echo -e "${GREEN}=============================================="
    echo "           清理完成！"
    echo "=============================================="
    echo -e "${NC}"
    echo "Clash X Pro 已被完全移除"
    echo "Clash Verge 保持不变"
    echo ""
    echo "如果您需要重新安装 Clash X Pro，请从官方渠道下载"
    echo ""
}

# 主函数
main() {
    # 检查系统
    check_macos
    
    # 显示横幅和信息
    show_banner
    show_files_to_remove
    
    # 用户确认
    confirm_removal
    
    echo ""
    log_info "开始清理 Clash X Pro..."
    echo ""
    
    # 执行清理步骤
    stop_clashx_processes
    unload_system_services
    remove_files
    verify_removal
    
    # 显示完成信息
    show_completion
}

# 错误处理
set -e
trap 'log_error "脚本执行过程中发生错误，请检查输出信息"' ERR

# 运行主函数
main "$@"
